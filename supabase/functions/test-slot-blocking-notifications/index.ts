import { serve } from 'https://deno.land/std@0.168.0/http/server.ts'
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    // Create Supabase client with service role
    const supabaseAdmin = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? '',
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false
        }
      }
    )

    // Get MSG91 configuration
    const authKey = Deno.env.get('MSG91_AUTH_KEY')
    const integratedNumber = Deno.env.get('MSG91_INTEGRATED_NUMBER') || '919211848599'

    console.log('🔍 Testing Slot Blocking Notifications Integration')
    console.log('Auth Key available:', !!authKey)
    console.log('Integrated Number:', integratedNumber)

    if (!authKey) {
      console.error('❌ MSG91_AUTH_KEY not found in environment variables')
      return new Response(
        JSON.stringify({ 
          error: 'MSG91_AUTH_KEY not configured',
          available_env_vars: Object.keys(Deno.env.toObject()).filter(key => key.includes('MSG91'))
        }),
        {
          status: 500,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        }
      )
    }

    // Parse request body to get test parameters
    let testBlockedSlotId = 'c0f3fd0c-e9a9-4a7e-8eb8-268b29a71350' // Default test slot
    let testPhone = '+919876543210' // Default test number
    
    try {
      const body = await req.json()
      if (body.blocked_slot_id) {
        testBlockedSlotId = body.blocked_slot_id
      }
      if (body.phone) {
        testPhone = body.phone
      }
    } catch {
      // Use defaults if no body
    }

    console.log('📱 Testing with blocked slot ID:', testBlockedSlotId)
    console.log('📱 Test phone number:', testPhone)

    // Test the notification generation function
    const { data: notificationData, error: notificationError } = await supabaseAdmin
      .rpc('send_slot_blocking_notification', { p_blocked_slot_id: testBlockedSlotId })

    if (notificationError) {
      console.error('❌ Error generating notification data:', notificationError)
      return new Response(
        JSON.stringify({ 
          error: 'Failed to generate notification data',
          details: notificationError.message
        }),
        {
          status: 500,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        }
      )
    }

    console.log('✅ Notification data generated successfully')
    console.log('Venue:', notificationData.venue_name)
    console.log('Court:', notificationData.court_name)
    console.log('Date:', notificationData.blocked_date)
    console.log('Time:', notificationData.time_range)
    console.log('Notifications to send:', notificationData.notifications_generated)

    // Test MSG91 template with first payload (if available)
    let testResult = null
    if (notificationData.msg91_payloads && notificationData.msg91_payloads.length > 0) {
      const testPayload = {
        ...notificationData.msg91_payloads[0].msg91_payload,
        payload: {
          ...notificationData.msg91_payloads[0].msg91_payload.payload,
          template: {
            ...notificationData.msg91_payloads[0].msg91_payload.payload.template,
            to_and_components: [
              {
                to: [testPhone], // Use test phone instead of real admin phone
                components: notificationData.msg91_payloads[0].msg91_payload.payload.template.to_and_components[0].components
              }
            ]
          }
        }
      }

      console.log('📤 Testing MSG91 API call with test phone number...')

      try {
        const response = await fetch('https://api.msg91.com/api/v5/whatsapp/whatsapp-outbound-message/bulk/', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'authkey': authKey
          },
          body: JSON.stringify(testPayload)
        })

        const responseText = await response.text()
        testResult = {
          status: response.status,
          ok: response.ok,
          response: responseText
        }

        if (response.ok) {
          console.log('✅ Test MSG91 API call successful')
        } else {
          console.log('❌ Test MSG91 API call failed:', responseText)
        }
      } catch (apiError) {
        console.error('❌ MSG91 API call error:', apiError)
        testResult = {
          error: apiError.message
        }
      }
    }

    return new Response(
      JSON.stringify({
        success: true,
        message: 'Slot blocking notifications test completed',
        test_parameters: {
          blocked_slot_id: testBlockedSlotId,
          test_phone: testPhone
        },
        notification_data: {
          venue_name: notificationData.venue_name,
          court_name: notificationData.court_name,
          blocked_date: notificationData.blocked_date,
          time_range: notificationData.time_range,
          reason: notificationData.reason,
          notifications_generated: notificationData.notifications_generated,
          template_used: notificationData.template_used
        },
        msg91_test_result: testResult,
        environment_check: {
          auth_key_available: !!authKey,
          integrated_number: integratedNumber,
          supabase_url: !!Deno.env.get('SUPABASE_URL'),
          service_role_key: !!Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')
        }
      }),
      {
        status: 200,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      }
    )

  } catch (error) {
    console.error('❌ Unexpected error in test function:', error)
    return new Response(
      JSON.stringify({ 
        error: 'Internal server error', 
        details: error.message 
      }),
      {
        status: 500,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      }
    )
  }
})

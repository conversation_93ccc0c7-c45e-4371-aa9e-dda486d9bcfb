import React, { useState, useEffect, useCallback } from 'react';
import { format } from 'date-fns';
import { Calendar as CalendarIcon, Clock, User } from 'lucide-react';
import { Calendar } from '@/components/ui/calendar';
import { Button } from '@/components/ui/button';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import AdminBookingForm from '@/components/AdminBookingForm';
import { supabase } from '@/integrations/supabase/client';
import { motion } from 'framer-motion';
import { toast } from '@/components/ui/use-toast';

interface TimeSlot {
  start_time: string;
  end_time: string;
  price: string;
  is_available: boolean;
  booking_type: 'court_based' | 'capacity_based';
  available_spots?: number;
  max_capacity?: number;
}
interface AdminBookingTabProps {
  userRole: string | null;
  adminVenues: {
    venue_id: string;
  }[];
}
interface Venue {
  id: string;
  name: string;
  courts: {
    id: string;
    name: string;
    hourly_rate: number;
  }[];
  allow_cash_payments: boolean;
}
interface Court {
  id: string;
  name: string;
  hourly_rate: number;
  venue_id: string;
}
const AdminBookingTab: React.FC<AdminBookingTabProps> = ({
  userRole,
  adminVenues
}) => {
  const [venues, setVenues] = useState<Venue[]>([]);
  const [selectedVenue, setSelectedVenue] = useState<Venue | null>(null);
  const [selectedDate, setSelectedDate] = useState<Date>(new Date());
  const [selectedCourtId, setSelectedCourtId] = useState<string>('');
  const [selectedCourtName, setSelectedCourtName] = useState<string>('');
  const [selectedSlots, setSelectedSlots] = useState<string[]>([]);
  const [selectedSlotPrices, setSelectedSlotPrices] = useState<Record<string, number>>({});
  const [availableTimeSlots, setAvailableTimeSlots] = useState<TimeSlot[]>([]);
  const [courtDetails, setCourtDetails] = useState<Court | null>(null);
  const [loading, setLoading] = useState(true);
  const [slots, setSlots] = useState<TimeSlot[]>([]);
  const [slotsLoading, setSlotsLoading] = useState(false);
  const [slotsError, setSlotsError] = useState<string | null>(null);
  const [lastRefresh, setLastRefresh] = useState<number>(Date.now());

  // Time utility functions
  const timeToMinutes = (timeString: string) => {
    const [hours, minutes] = timeString.split(':').map(Number);
    return hours * 60 + minutes;
  };

  const formatTime = (timeString: string) => {
    if (!timeString) return '';
    const [hours, minutes] = timeString.split(':');
    const hour = parseInt(hours, 10);
    const ampm = hour >= 12 ? 'PM' : 'AM';
    const hour12 = hour % 12 || 12;
    return `${hour12}:${minutes} ${ampm}`;
  };

  const parseFormattedTime = (formattedTime: string) => {
    const [time, ampm] = formattedTime.split(' ');
    const [hours, minutes] = time.split(':');
    let hour = parseInt(hours, 10);

    if (ampm === 'PM' && hour !== 12) {
      hour += 12;
    } else if (ampm === 'AM' && hour === 12) {
      hour = 0;
    }

    return `${hour.toString().padStart(2, '0')}:${minutes}`;
  };

  // Slot selection logic from BookingPage.tsx
  const areSlotsContinuous = (slots: string[]) => {
    if (slots.length <= 1) return true;

    const sortedSlots = slots.sort((a, b) => {
      if (!a || !b || typeof a !== 'string' || typeof b !== 'string') return 0;
      const startTimeA = a.split(' - ')[0];
      const startTimeB = b.split(' - ')[0];
      return timeToMinutes(startTimeA) - timeToMinutes(startTimeB);
    });

    for (let i = 0; i < sortedSlots.length - 1; i++) {
      if (!sortedSlots[i] || !sortedSlots[i + 1] || typeof sortedSlots[i] !== 'string' || typeof sortedSlots[i + 1] !== 'string') continue;
      const currentEndTime = sortedSlots[i].split(' - ')[1];
      const nextStartTime = sortedSlots[i + 1].split(' - ')[0];

      if (currentEndTime !== nextStartTime) {
        return false;
      }
    }
    return true;
  };

  const wouldCreateValidSelection = (newSlot: string, currentSlots: string[]) => {
    if (currentSlots.length === 0) return true;
    const testSlots = [...currentSlots, newSlot];
    return areSlotsContinuous(testSlots);
  };

  const getSelectableSlots = (currentSlots: string[], availableSlots: TimeSlot[]) => {
    if (currentSlots.length === 0) {
      return availableSlots.map(slot => `${formatTime(slot.start_time)} - ${formatTime(slot.end_time)}`);
    }

    const sortedCurrentSlots = currentSlots.sort((a, b) => {
      if (!a || !b || typeof a !== 'string' || typeof b !== 'string') return 0;
      const startTimeA = a.split(' - ')[0];
      const startTimeB = b.split(' - ')[0];
      return timeToMinutes(startTimeA) - timeToMinutes(startTimeB);
    });

    if (sortedCurrentSlots.length === 0 || !sortedCurrentSlots[0] || typeof sortedCurrentSlots[0] !== 'string') return [];

    const firstSlotStart = sortedCurrentSlots[0].split(' - ')[0];
    const lastSlotEnd = sortedCurrentSlots[sortedCurrentSlots.length - 1].split(' - ')[1];

    return availableSlots
      .map(slot => `${formatTime(slot.start_time)} - ${formatTime(slot.end_time)}`)
      .filter(slotDisplay => {
        if (currentSlots.includes(slotDisplay)) return true;

        if (!slotDisplay || typeof slotDisplay !== 'string') return false;
        const slotStart = slotDisplay.split(' - ')[0];
        const slotEnd = slotDisplay.split(' - ')[1];

        return slotEnd === firstSlotStart || slotStart === lastSlotEnd;
      });
  };

  // Enhanced slot selection logic from BookingPage.tsx
  const handleSlotClick = (slot: TimeSlot) => {
    if (!slot.is_available) return;

    const slotDisplay = `${formatTime(slot.start_time)} - ${formatTime(slot.end_time)}`;
    const slotPrice = parseFloat(slot.price);

    // For capacity-based sports, only allow single slot selection
    if (slot.booking_type === 'capacity_based') {
      if (selectedSlots.includes(slotDisplay)) {
        setSelectedSlots([]);
        setSelectedSlotPrices({});
      } else {
        setSelectedSlots([slotDisplay]);
        setSelectedSlotPrices({ [slotDisplay]: slotPrice });
      }
      return;
    }

    // Enhanced logic for court-based sports with consecutive validation
    if (selectedSlots.includes(slotDisplay)) {
      const newSlots = selectedSlots.filter(s => s !== slotDisplay);
      const newSelectedSlotPrices = { ...selectedSlotPrices };
      delete newSelectedSlotPrices[slotDisplay];

      if (newSlots.length > 0 && !areSlotsContinuous(newSlots)) {
        toast({
          title: "Invalid selection",
          description: "Removing this slot would create a gap. Please select consecutive time slots only.",
          variant: "destructive",
        });
        return;
      }

      setSelectedSlots(newSlots);
      setSelectedSlotPrices(newSelectedSlotPrices);
    } else {
      // Regular single slot selection
      if (!wouldCreateValidSelection(slotDisplay, selectedSlots)) {
        toast({
          title: "Invalid selection",
          description: "Please select consecutive time slots only.",
          variant: "destructive",
        });
        return;
      }

      const updatedSlots = [...selectedSlots, slotDisplay];
      const sortedSlots = updatedSlots.sort((a, b) => {
        const startTimeA = a.split(' - ')[0];
        const startTimeB = b.split(' - ')[0];
        return timeToMinutes(startTimeA) - timeToMinutes(startTimeB);
      });

      setSelectedSlots(sortedSlots);
      setSelectedSlotPrices({
        ...selectedSlotPrices,
        [slotDisplay]: slotPrice
      });
    }
  };

  useEffect(() => {
    fetchVenues();
  }, [userRole, adminVenues]);
  useEffect(() => {
    if (selectedCourtId) {
      fetchCourtDetails(selectedCourtId);
    }
  }, [selectedCourtId]);

  const fetchVenues = async () => {
    try {
      setLoading(true);
      let query = supabase.from('venues').select(`
        id,
        name,
        allow_cash_payments,
        courts:courts(id, name, hourly_rate)
      `).eq('is_active', true);

      // If admin (not super admin), filter to only show their venues
      if (userRole === 'admin' && adminVenues.length > 0) {
        const venueIds = adminVenues.map(v => v.venue_id);
        query = query.in('id', venueIds);
      }
      const {
        data,
        error
      } = await query;
      if (error) throw error;

      // Transform data to match the Venue interface
      const transformedVenues = data?.map(venue => ({
        id: venue.id,
        name: venue.name,
        courts: venue.courts || [],
        allow_cash_payments: venue.allow_cash_payments !== false // Default to true if null
      })) || [];
      setVenues(transformedVenues);

      // Set the first venue as selected by default
      if (transformedVenues.length > 0 && !selectedVenue) {
        setSelectedVenue(transformedVenues[0]);
        // Also select the first court
        if (transformedVenues[0].courts.length > 0) {
          const firstCourt = transformedVenues[0].courts[0];
          setSelectedCourtId(firstCourt.id);
          setSelectedCourtName(firstCourt.name);
        }
      }
    } catch (error) {
      console.error('Error fetching venues:', error);
    } finally {
      setLoading(false);
    }
  };
  const fetchCourtDetails = async (courtId: string) => {
    try {
      const {
        data,
        error
      } = await supabase.from('courts').select('id, name, hourly_rate, venue_id').eq('id', courtId).single();
      if (error) throw error;
      setCourtDetails(data);
    } catch (err) {
      console.error('Error fetching court details:', err);
    }
  };

  // Handle court selection
  const handleCourtSelect = (courtId: string) => {
    const venue = selectedVenue;
    if (venue) {
      const court = venue.courts.find(c => c.id === courtId);
      if (court) {
        setSelectedCourtId(courtId);
        setSelectedCourtName(court.name);
        setSelectedSlots([]); // Reset selected slots when changing courts
        setSelectedSlotPrices({});
      }
    }
  };

  const handleBookingComplete = () => {
    setSelectedSlots([]);
    setSelectedSlotPrices({});
    setLastRefresh(Date.now()); // Force refresh after booking
  };

  // Add padTime helper
  const padTime = (t: string) => t.length === 5 ? t + ':00' : t;

  // Use unified availability function for consistency
  const fetchAvailability = async (courtId: string, date: string) => {
    try {
      setSlotsLoading(true);
      setSlotsError(null);

      console.log(`AdminBookingTab - Fetching availability for court ${courtId} on date ${date}`);

      // Use unified availability function that handles both court-based and capacity-based sports
      const { data, error } = await supabase.rpc('get_unified_availability' as any, {
        p_court_id: courtId,
        p_date: date
      });
      if (error) throw error;

      console.log('AdminBookingTab - Unified availability data:', data);

      // The unified function already handles all availability logic including:
      // - Court group logic for court-based sports
      // - Capacity checking for capacity-based sports (like Swimming)
      // - Blocked slots checking
      // Transform the data to match the expected interface
      const slotsWithStatus = (data as any)?.map((slot: any) => ({
        ...slot,
        is_booked: !slot.is_available && slot.booking_type !== 'capacity_based',
        is_blocked: false, // Unified function already handles blocked slots in is_available
        blocked_court_id: undefined,
        available_spots: slot.available_spots || (slot.is_available ? 1 : 0),
        max_capacity: slot.max_capacity || 1,
        booking_type: slot.booking_type || 'court_based'
      })) || [];

      setSlots(slotsWithStatus);
      setAvailableTimeSlots(slotsWithStatus);
    } catch (error: any) {
      setSlotsError(error.message || 'Failed to load availability');
      setSlots([]);
    } finally {
      setSlotsLoading(false);
    }
  };

  useEffect(() => {
    if (selectedCourtId && selectedDate) {
      fetchAvailability(selectedCourtId, format(selectedDate, 'yyyy-MM-dd'));
    }
  }, [selectedCourtId, selectedDate, lastRefresh]);

  // Set up real-time subscription for bookings and blocked slots
  useEffect(() => {
    if (!selectedCourtId || !selectedDate) return;

    const dateStr = format(selectedDate, 'yyyy-MM-dd');
    console.log(`AdminBookingTab - Setting up real-time subscriptions for court ${selectedCourtId} on ${dateStr}`);

    // Create unique channel names to avoid conflicts
    const channelId = `admin_booking_tab_${selectedCourtId}_${dateStr}_${Date.now()}`;

    // Bookings channel subscription for real-time updates
    const bookingsChannel = supabase.channel(`bookings_${channelId}`)
      .on('postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'bookings',
          filter: `booking_date=eq.${dateStr}`
        },
        (payload) => {
          console.log('AdminBookingTab - Booking change detected:', payload);
          // Refresh availability when bookings change
          fetchAvailability(selectedCourtId, dateStr);
        }
      )
      .subscribe();

    // Blocked slots channel subscription for real-time updates
    const blockedSlotsChannel = supabase.channel(`blocked_slots_${channelId}`)
      .on('postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'blocked_slots',
          filter: `date=eq.${dateStr}`
        },
        (payload) => {
          console.log('AdminBookingTab - Blocked slot change detected:', payload);
          // Refresh availability when blocked slots change
          fetchAvailability(selectedCourtId, dateStr);
        }
      )
      .subscribe();

    // Listen for custom booking completion events
    const handleBookingComplete = (event: any) => {
      console.log('AdminBookingTab - Received booking completion event', event.detail);
      fetchAvailability(selectedCourtId, dateStr);
    };

    const handleAvailabilityRefresh = (event: any) => {
      console.log('AdminBookingTab - Received immediate availability refresh event', event.detail);
      fetchAvailability(selectedCourtId, dateStr);
    };

    window.addEventListener('bookingCompleted', handleBookingComplete);
    window.addEventListener('availabilityRefresh', handleAvailabilityRefresh);

    return () => {
      console.log('AdminBookingTab - Cleaning up real-time subscriptions');
      supabase.removeChannel(bookingsChannel);
      supabase.removeChannel(blockedSlotsChannel);
      window.removeEventListener('bookingCompleted', handleBookingComplete);
      window.removeEventListener('availabilityRefresh', handleAvailabilityRefresh);
    };
  }, [selectedCourtId, selectedDate]);

  if (loading) {
    return <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-sport-green"></div>
      </div>;
  }
  if (venues.length === 0) {
    return <div className="text-center py-12 bg-gray-50 rounded-lg">
        <p className="text-gray-600">No venues available</p>
      </div>;
  }
  return <div>
      <h2 className="text-xl font-semibold mb-6">Book for Customer</h2>
      
      <div className="grid grid-cols-1 md:grid-cols-5 gap-6">
        {/* Left panel - Venue and date selection */}
        <div className="md:col-span-2">
          <div className="bg-emerald-800 rounded-md shadow p-4 mb-6">
            <h3 className="text-md font-medium mb-3">Select Venue and Court</h3>
            
            {/* Venue Selection */}
            <div className="mb-4">
              <label className="block text-sm font-medium text-white mb-1">
                Venue
              </label>
              <select value={selectedVenue?.id || ''} onChange={e => {
              const venueId = e.target.value;
              const venue = venues.find(v => v.id === venueId);
              if (venue) {
                setSelectedVenue(venue);
                // Select first court of the new venue
                if (venue.courts.length > 0) {
                  const firstCourt = venue.courts[0];
                  setSelectedCourtId(firstCourt.id);
                  setSelectedCourtName(firstCourt.name);
                  setSelectedSlots([]);
                  setSelectedSlotPrices({});
                }
              }
            }} className="w-full px-3 py-2 border border-black  rounded-md">
                {venues.map(venue => <option key={venue.id} value={venue.id}>
                    {venue.name}
                  </option>)}
              </select>
            </div>
            
            {/* Court Selection */}
            {selectedVenue && <div className="mb-4">
                <label className="block text-sm font-medium text-white mb-1">
                  Court
                </label>
                <div className="grid grid-cols-2 gap-2">
                  {selectedVenue.courts.map(court => <Button key={court.id} variant={selectedCourtId === court.id ? 'default' : 'outline'} onClick={() => handleCourtSelect(court.id)} className="text-xs h-9 justify-start overflow-hidden">
                      {court.name}
                    </Button>)}
                </div>
              </div>}
            
            {/* Date Selection */}
            <div>
              <label className="block text-sm font-medium text-white mb-1">
                Date
              </label>
              <Popover>
                <PopoverTrigger asChild>
                  <Button variant="outline" className="w-full justify-start">
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {format(selectedDate, 'PPP')}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0">
                  <Calendar mode="single" selected={selectedDate} onSelect={date => {
                  if (date) {
                    setSelectedDate(date);
                    setSelectedSlots([]);
                    setSelectedSlotPrices({});
                  }
                }} initialFocus disabled={date => date < new Date(new Date().setHours(0, 0, 0, 0))} />
                </PopoverContent>
              </Popover>
            </div>
          </div>
          
          {/* Enhanced Slot Selection UI */}
          <div className="bg-emerald-800 rounded-md shadow p-4">
            <h3 className="text-md font-medium mb-3">Available Time Slots</h3>

            {selectedCourtId ? (
              slotsLoading ? (
                <div className="flex justify-center items-center py-8">
                  <Clock className="h-8 w-8 animate-spin text-emerald-400" />
                  <span className="ml-2 text-gray-400">Loading slots...</span>
                </div>
              ) : slotsError ? (
                <div className="text-center py-8">
                  <p className="text-red-400">{slotsError}</p>
                </div>
              ) : availableTimeSlots.length === 0 ? (
                <div className="text-center py-8">
                  <p className="text-gray-400">No slots available for this date</p>
                </div>
              ) : (
                <>
                  {/* Consecutive Slot Selection Info */}
                  {availableTimeSlots.some(slot => slot.booking_type === 'court_based') && (
                    <div className="mb-4 p-3 bg-blue-900/20 rounded-lg border border-blue-800/30">
                      <div className="flex items-start gap-2">
                        <div className="w-4 h-4 bg-blue-500 rounded-full mt-0.5 flex-shrink-0"></div>
                        <div>
                          <h4 className="text-sm font-medium text-blue-300 mb-1">Consecutive Slot Selection</h4>
                          <p className="text-xs text-gray-400 mb-2">
                            You can select multiple time slots, but they must be consecutive (no gaps).
                          </p>
                          <p className="text-xs text-blue-300">
                            💡 <strong>Tip:</strong> Select consecutive slots for longer bookings
                          </p>
                        </div>
                      </div>
                    </div>
                  )}

                  {/* Slot Grid */}
                  <div className="grid grid-cols-1 gap-3">
                    {availableTimeSlots.map((slot, index) => {
                      const slotDisplay = `${formatTime(slot.start_time)} - ${formatTime(slot.end_time)}`;
                      const isSelected = selectedSlots.includes(slotDisplay);
                      const selectableSlots = getSelectableSlots(selectedSlots, availableTimeSlots);
                      const isSelectable = slot.is_available && (selectedSlots.length === 0 || selectableSlots.includes(slotDisplay));
                      const isDisabledByRule = slot.is_available && !isSelected && !isSelectable;

                      return (
                        <motion.button
                          key={`${slot.start_time}-${slot.end_time}`}
                          initial={{ opacity: 0, y: 20 }}
                          animate={{ opacity: 1, y: 0 }}
                          transition={{ delay: index * 0.05 }}
                          whileHover={{ scale: isSelectable ? 1.02 : 1 }}
                          whileTap={{ scale: isSelectable ? 0.98 : 1 }}
                          disabled={!isSelectable}
                          onClick={() => handleSlotClick(slot)}
                          className={`
                            relative p-4 rounded-xl border-2 transition-all duration-200 text-left
                            min-h-[80px] flex flex-col justify-between
                            ${!slot.is_available
                              ? 'bg-red-900/20 border-red-800/50 text-red-300 cursor-not-allowed'
                              : isSelected
                                ? 'bg-emerald-600/20 border-emerald-500 text-emerald-100 shadow-lg shadow-emerald-900/20'
                                : isDisabledByRule
                                  ? 'bg-gray-800/30 border-gray-700/50 text-gray-500 cursor-not-allowed opacity-50'
                                  : 'bg-gray-800/50 border-gray-600/50 hover:border-emerald-500/50 hover:bg-gray-750/50 text-gray-200'
                            }
                            ${isSelected ? 'ring-2 ring-emerald-400/50' : ''}
                          `}
                        >
                          {/* Time and Price Row */}
                          <div className="flex justify-between items-start">
                            <div>
                              <div className="font-semibold text-base">
                                {formatTime(slot.start_time)}
                              </div>
                              <div className="text-sm opacity-75">
                                to {formatTime(slot.end_time)}
                              </div>
                            </div>

                            <div className="text-right">
                              <div className="font-bold text-base">
                                ₹{parseFloat(slot.price).toFixed(0)}
                              </div>
                              {slot.booking_type === 'capacity_based' && (
                                <div className="text-xs opacity-75 flex items-center gap-1">
                                  <User size={10} />
                                  {slot.available_spots || 0}/{slot.max_capacity || 0}
                                </div>
                              )}
                            </div>
                          </div>

                          {/* Status Indicators */}
                          <div className="flex justify-between items-center mt-2">
                            <div className="flex items-center gap-2">
                              <div className={`w-3 h-3 rounded-full transition-all ${
                                isSelected
                                  ? 'bg-emerald-400 shadow-lg shadow-emerald-400/50'
                                  : slot.is_available
                                    ? 'bg-gray-600 border border-gray-500'
                                    : 'bg-red-600'
                              }`} />

                              <span className="text-xs font-medium">
                                {!slot.is_available
                                  ? 'Booked'
                                  : isSelected
                                    ? 'Selected'
                                    : isDisabledByRule
                                      ? 'Not consecutive'
                                      : 'Available'
                                }
                              </span>
                            </div>

                            {slot.booking_type === 'capacity_based' && (
                              <div className="text-xs bg-blue-900/30 text-blue-300 px-2 py-1 rounded-full">
                                Capacity
                              </div>
                            )}
                          </div>
                        </motion.button>
                      );
                    })}
                  </div>

                  {/* Selected Slots Summary */}
                  {selectedSlots.length > 0 && (
                    <div className="mt-4 p-3 bg-emerald-900/20 rounded-lg border border-emerald-700/30">
                      <h4 className="text-sm font-medium text-emerald-300 mb-2">Selected Slots</h4>
                      <div className="space-y-1">
                        {selectedSlots.map(slot => (
                          <div key={slot} className="flex justify-between text-xs">
                            <span className="text-gray-300">{slot}</span>
                            <span className="text-emerald-300">₹{selectedSlotPrices[slot]?.toFixed(0) || '0'}</span>
                          </div>
                        ))}
                      </div>
                      <div className="mt-2 pt-2 border-t border-emerald-700/30 flex justify-between text-sm font-medium">
                        <span className="text-emerald-300">Total</span>
                        <span className="text-emerald-300">
                          ₹{Object.values(selectedSlotPrices).reduce((sum, price) => sum + price, 0).toFixed(0)}
                        </span>
                      </div>
                    </div>
                  )}
                </>
              )
            ) : (
              <p className="text-gray-500 text-center py-4">Select a court to view availability</p>
            )}
          </div>
        </div>
        
        {/* Right panel - Booking form */}
        <div className="md:col-span-3">
          {selectedCourtId && courtDetails && selectedVenue && selectedSlots.length > 0 ? (
            <AdminBookingForm
              courtId={selectedCourtId}
              courtName={selectedCourtName}
              venueName={selectedVenue.name}
              venueId={courtDetails.venue_id}
              date={format(selectedDate, 'yyyy-MM-dd')}
              selectedSlot={{
                start_time: parseFormattedTime(selectedSlots[0].split(' - ')[0]),
                end_time: parseFormattedTime(selectedSlots[selectedSlots.length - 1].split(' - ')[1]),
                is_available: true
              }}
              totalPrice={Object.values(selectedSlotPrices).reduce((sum, price) => sum + price, 0)}
              onBookingComplete={handleBookingComplete}
              allowCashPayments={selectedVenue.allow_cash_payments}
            />
          ) : (
            <div className="bg-white rounded-md shadow p-4">
              <p className="text-gray-500 text-center py-8">
                Select a venue, court, and time slots to create a booking
              </p>
            </div>
          )}
        </div>
      </div>
    </div>;
};
export default AdminBookingTab;

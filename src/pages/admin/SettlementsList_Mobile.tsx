import React, { useState, useEffect, useCallback } from 'react';
import { useAuth } from '@/context/AuthContext';
import { Link, useNavigate } from 'react-router-dom';
import {
  ArrowLeft, Calendar, DollarSign, Clock, 
  CheckCircle, AlertCircle, Eye, Plus,
  Loader2, RefreshCw, ChevronRight, Info, TrendingUp, TrendingDown, BookOpen,
  Settings, Edit, Download
} from 'lucide-react';
import { useIsMobile } from '@/hooks/use-mobile';
import { supabase } from '@/integrations/supabase/client';
import { format, parseISO } from 'date-fns';
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription } from "@/components/ui/dialog";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { toast } from "react-hot-toast";
import SettlementManagementModal from '@/components/admin/SettlementManagementModal';
import * as XLSX from 'xlsx';

interface Settlement {
  settlement_id: string;
  venue_id: string;
  venue_name: string;
  settlement_week_start: string;
  settlement_week_end: string;
  settlement_reference: string;
  total_gross_revenue: number;
  total_platform_fees: number;
  total_net_revenue: number;
  total_bookings: number;
  total_refunds: number;
  status: 'pending' | 'processed' | 'settled';
  expected_settlement_date: string;
  created_at: string;
  notes?: string;
  processed_at?: string;
  processed_by_name?: string;
  settled_at?: string;
  settled_by_name?: string;
}

interface SettlementsListMobileProps {
  userRole?: string;
  adminVenues?: Array<{ venue_id: string; venue_name?: string }>;
}

const SettlementsList_Mobile: React.FC<SettlementsListMobileProps> = ({
  userRole: propUserRole,
  adminVenues: propAdminVenues
}) => {
  const { user, userRole: contextUserRole } = useAuth();
  const navigate = useNavigate();
  const isMobile = useIsMobile();

  // Use props if provided, otherwise use context
  const userRole = propUserRole || contextUserRole;
  const [adminVenues, setAdminVenues] = useState<Array<{ venue_id: string; venue_name?: string }>>(propAdminVenues || []);

  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [settlements, setSettlements] = useState<Settlement[]>([]);
  const [selectedVenue, setSelectedVenue] = useState<string>('all');
  const [selectedSettlement, setSelectedSettlement] = useState<Settlement | null>(null);
  const [detailsOpen, setDetailsOpen] = useState(false);
  const [managementOpen, setManagementOpen] = useState(false);
  const [downloadingReport, setDownloadingReport] = useState(false);
  const [couponAnalytics, setCouponAnalytics] = useState<{
    totalCouponBookings: number;
    totalDiscountGiven: number;
    couponBreakdown: Array<{code: string; count: number; totalDiscount: number}>;
  } | null>(null);
  const [correctedGrossRevenue, setCorrectedGrossRevenue] = useState<number | null>(null);
  // HUDLE APPROACH: Online vs Offline Metrics State
  const [onlineMetrics, setOnlineMetrics] = useState<{
    count: number;
    grossRevenue: number;
    netSettlement: number;
  } | null>(null);
  const [offlineMetrics, setOfflineMetrics] = useState<{
    count: number;
    grossRevenue: number;
    netAmount: number;
  } | null>(null);
  // PHASE 3: Cancelled Booking Count State
  const [cancelledBookingCount, setCancelledBookingCount] = useState<number | null>(null);

  // Function to calculate correct gross revenue (before discounts)
  const calculateCorrectGrossRevenue = (settlement: Settlement, bookingsData: any[]) => {
    if (!bookingsData || bookingsData.length === 0) {
      return settlement.total_gross_revenue; // Fallback to stored value
    }

    // Filter bookings for this settlement's venue and date range
    const settlementBookings = bookingsData.filter(booking => {
      const bookingVenueId = booking.court_data?.venue?.id;
      return bookingVenueId === settlement.venue_id;
    });

    // Calculate gross revenue using original amounts (before discounts)
    const correctGrossRevenue = settlementBookings.reduce((sum, booking) => {
      if (booking.status !== 'confirmed') return sum;

      const couponUsageArray = booking.coupon_data;
      const couponUsage = couponUsageArray && Array.isArray(couponUsageArray) ? couponUsageArray[0] : null;
      const originalAmount = couponUsage ? (Number(couponUsage.original_price) || 0) : (Number(booking.total_price) || 0);
      return sum + originalAmount;
    }, 0);

    return correctGrossRevenue > 0 ? correctGrossRevenue : settlement.total_gross_revenue;
  };

  // HUDLE APPROACH: Calculate Online vs Offline Metrics + PHASE 3: Cancelled Bookings
  const calculateOnlineOfflineMetrics = async (settlement: Settlement) => {
    try {
      // PHASE 3: Fetch booking data including cancelled bookings
      const { data: rpcData, error: bookingError } = await supabase
        .rpc('get_bookings_with_coupons_including_cancelled', {
          start_date: settlement.settlement_week_start,
          end_date: settlement.settlement_week_end
        });

      if (bookingError) {
        console.error('Error fetching booking data:', bookingError);
        return;
      }

      // Filter all bookings for this specific venue
      const allVenueBookings = rpcData?.filter(booking =>
        booking.court_data?.venue?.id === settlement.venue_id
      ) || [];

      // Separate confirmed/completed bookings from cancelled bookings
      const settlementBookings = allVenueBookings.filter(booking =>
        ['confirmed', 'completed'].includes(booking.status)
      );

      // PHASE 3: Count cancelled bookings for this venue
      const cancelledBookings = allVenueBookings.filter(booking =>
        booking.status === 'cancelled'
      );

      // Separate online and offline bookings
      const onlineBookings = settlementBookings.filter(b => b.payment_method === 'online');
      const offlineBookings = settlementBookings.filter(b => b.payment_method === 'cash');

      // Calculate online metrics
      let onlineGrossRevenue = 0;
      let onlineNetSettlement = 0;

      onlineBookings.forEach(booking => {
        const couponUsageArray = booking.coupon_data;
        const couponUsage = couponUsageArray && Array.isArray(couponUsageArray) ? couponUsageArray[0] : null;
        const originalAmount = couponUsage ? (Number(couponUsage.original_price) || 0) : (Number(booking.total_price) || 0);
        const totalPrice = Number(booking.total_price) || 0;
        const platformFeePercent = booking.court_data?.venue?.platform_fee_percentage || 5;
        const platformFee = originalAmount * (platformFeePercent / 100);

        onlineGrossRevenue += originalAmount;
        onlineNetSettlement += (totalPrice - platformFee);
      });

      // Calculate offline metrics
      let offlineGrossRevenue = 0;
      let offlineNetAmount = 0;

      offlineBookings.forEach(booking => {
        const couponUsageArray = booking.coupon_data;
        const couponUsage = couponUsageArray && Array.isArray(couponUsageArray) ? couponUsageArray[0] : null;
        const originalAmount = couponUsage ? (Number(couponUsage.original_price) || 0) : (Number(booking.total_price) || 0);
        const totalPrice = Number(booking.total_price) || 0;

        offlineGrossRevenue += originalAmount;
        offlineNetAmount += totalPrice;
      });

      // Set the metrics
      setOnlineMetrics({
        count: onlineBookings.length,
        grossRevenue: onlineGrossRevenue,
        netSettlement: onlineNetSettlement
      });

      setOfflineMetrics({
        count: offlineBookings.length,
        grossRevenue: offlineGrossRevenue,
        netAmount: offlineNetAmount
      });

      // PHASE 3: Set cancelled booking count
      setCancelledBookingCount(cancelledBookings.length);

      console.log('📊 Settlement metrics calculated:', {
        online: { count: onlineBookings.length, grossRevenue: onlineGrossRevenue, netSettlement: onlineNetSettlement },
        offline: { count: offlineBookings.length, grossRevenue: offlineGrossRevenue, netAmount: offlineNetAmount },
        cancelled: cancelledBookings.length
      });

    } catch (error) {
      console.error('Error calculating online/offline metrics:', error);
    }
  };

  // Redirect to desktop if not mobile
  useEffect(() => {
    if (!isMobile) {
      navigate('/admin/settlements');
    }
  }, [isMobile, navigate]);

  // FIX: Fetch admin venues for venue filter
  useEffect(() => {
    const fetchAdminVenues = async () => {
      if (!user?.id || propAdminVenues) return; // Skip if props provided

      try {
        if (userRole === 'admin') {
          const { data, error } = await supabase.rpc('get_admin_venues');
          if (!error && data) {
            // Fetch venue names
            const venueIds = data.map((v: { venue_id: string }) => v.venue_id);
            const { data: venueDetails, error: venueError } = await supabase
              .from('venues')
              .select('id, name')
              .in('id', venueIds)
              .eq('is_active', true);

            if (!venueError && venueDetails) {
              const venuesWithNames = data.map((v: { venue_id: string }) => ({
                venue_id: v.venue_id,
                venue_name: venueDetails.find(vd => vd.id === v.venue_id)?.name
              }));
              setAdminVenues(venuesWithNames);
            }
          }
        }
      } catch (error) {
        console.error('Error fetching admin venues:', error);
      }
    };

    fetchAdminVenues();
  }, [user?.id, userRole, propAdminVenues]);

  // Fetch settlements data with proper venue filtering
  const fetchSettlements = useCallback(async (showRefreshIndicator = false) => {
    if (!user?.id) return;
    
    if (showRefreshIndicator) {
      setRefreshing(true);
    } else {
      setLoading(true);
    }

    try {
      let settlementsData: Settlement[] = [];

      if (userRole === 'super_admin') {
        // Super admin can see all settlements
        const { data, error } = await supabase
          .rpc('get_admin_settlements', {
            p_venue_id: null,
            p_status: null,
            p_limit: 50
          });

        if (error) throw error;
        settlementsData = data || [];

      } else if (userRole === 'admin') {
        // Venue admin can only see their venue's settlements
        
        // First get the venues this admin manages
        const { data: venueData, error: venueError } = await supabase
          .rpc('get_admin_venues');

        if (venueError) {
          console.error('Error fetching admin venues:', venueError);
          throw venueError;
        }

        if (!venueData || venueData.length === 0) {
          console.log('No venues found for admin');
          setSettlements([]);
          return;
        }

        // FIX: Apply venue filter for settlements
        const allSettlements: Settlement[] = [];

        // Filter venues based on selectedVenue
        const venuesToFetch = selectedVenue === 'all' ? venueData : venueData.filter(v => v.venue_id === selectedVenue);

        for (const venue of venuesToFetch) {
          const { data: venueSettlements, error: settlementError } = await supabase
            .rpc('get_admin_settlements', {
              p_venue_id: venue.venue_id,
              p_status: null,
              p_limit: 50
            });

          if (settlementError) {
            console.error(`Error fetching settlements for venue ${venue.venue_id}:`, settlementError);
            continue; // Skip this venue but continue with others
          }

          if (venueSettlements && venueSettlements.length > 0) {
            allSettlements.push(...venueSettlements);
          }
        }

        settlementsData = allSettlements;
      }

      setSettlements(settlementsData);

    } catch (error) {
      console.error('Error fetching settlements:', error);
      toast.error('Failed to load settlements');
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  }, [user?.id, userRole, selectedVenue]);

  useEffect(() => {
    fetchSettlements();
  }, [fetchSettlements]);
  
  // Real-time subscription for settlements
  useEffect(() => {
    if (!user?.id) return;

    const channel = supabase
      .channel('public:settlements')
      .on(
        'postgres_changes',
        { event: '*', schema: 'public', table: 'settlements' },
        (payload) => {
          console.log('Real-time settlement update received!', payload);
          // Auto-refresh settlements without toast notification
          fetchSettlements(true);
        }
      )
      .subscribe();

    return () => {
      supabase.removeChannel(channel);
    };
  }, [user?.id, fetchSettlements]);

  // Get status badge color and icon
  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'pending':
        return { color: 'bg-yellow-500', icon: <Clock className="w-3 h-3" />, text: 'Pending' };
      case 'processed':
        return { color: 'bg-blue-500', icon: <AlertCircle className="w-3 h-3" />, text: 'Processed' };
      case 'settled':
        return { color: 'bg-green-500', icon: <CheckCircle className="w-3 h-3" />, text: 'Settled' };
      default:
        return { color: 'bg-gray-500', icon: <Clock className="w-3 h-3" />, text: status };
    }
  };

  // Open settlement details
  const openDetails = (settlement: Settlement) => {
    setSelectedSettlement(settlement);
    setDetailsOpen(true);
    fetchCouponAnalytics(settlement);
    // HUDLE APPROACH: Calculate online/offline metrics when opening details
    calculateOnlineOfflineMetrics(settlement);
  };

  // Close settlement details
  const closeDetails = () => {
    setDetailsOpen(false);
    setSelectedSettlement(null);
    setCorrectedGrossRevenue(null);
    setCouponAnalytics(null);
    // HUDLE APPROACH: Reset online/offline metrics
    setOnlineMetrics(null);
    setOfflineMetrics(null);
    // PHASE 3: Reset cancelled booking count
    setCancelledBookingCount(null);
  };

  // Open settlement management (super admin only)
  const openManagement = (settlement: Settlement) => {
    setSelectedSettlement(settlement);
    setManagementOpen(true);
  };

  // Handle settlement update
  const handleSettlementUpdate = () => {
    fetchSettlements(true);
    setManagementOpen(false);
    setSelectedSettlement(null);
  };

  // Fetch coupon analytics for selected settlement
  const fetchCouponAnalytics = async (settlement: Settlement) => {
    try {
      console.log('🔄 Mobile - Fetching coupon analytics for settlement:', settlement.settlement_reference);

      // Use RPC function to get booking data with coupons for this settlement
      const { data: rpcData, error } = await supabase
        .rpc('get_bookings_with_coupons', {
          start_date: settlement.settlement_week_start,
          end_date: settlement.settlement_week_end
        });

      if (error) throw error;

      // Filter bookings for this specific venue and status
      const settlementBookings = rpcData?.filter(booking =>
        booking.court_data?.venue?.id === settlement.venue_id &&
        ['confirmed', 'completed'].includes(booking.status)
      ) || [];

      // Calculate coupon analytics
      const bookingsWithCoupons = settlementBookings.filter(booking =>
        booking.coupon_data && Array.isArray(booking.coupon_data) && booking.coupon_data.length > 0
      );

      const totalDiscountGiven = bookingsWithCoupons.reduce((sum, booking) => {
        const couponUsage = booking.coupon_data[0];
        return sum + (Number(couponUsage.discount_applied) || 0);
      }, 0);

      // Group by coupon code
      const couponBreakdown = bookingsWithCoupons.reduce((acc, booking) => {
        const couponUsage = booking.coupon_data[0];
        const code = couponUsage.coupon?.code || 'Unknown';
        const discount = Number(couponUsage.discount_applied) || 0;

        if (!acc[code]) {
          acc[code] = { code, count: 0, totalDiscount: 0 };
        }
        acc[code].count++;
        acc[code].totalDiscount += discount;
        return acc;
      }, {} as Record<string, {code: string; count: number; totalDiscount: number}>);

      setCouponAnalytics({
        totalCouponBookings: bookingsWithCoupons.length,
        totalDiscountGiven,
        couponBreakdown: Object.values(couponBreakdown)
      });

      console.log('📊 Mobile - Coupon analytics calculated:', {
        totalBookings: settlementBookings.length,
        couponBookings: bookingsWithCoupons.length,
        totalDiscount: totalDiscountGiven,
        breakdown: Object.values(couponBreakdown)
      });

      // Calculate and store corrected gross revenue
      const correctedRevenue = calculateCorrectGrossRevenue(settlement, settlementBookings);
      setCorrectedGrossRevenue(correctedRevenue);

      console.log('💰 Revenue Correction:', {
        settlementId: settlement.settlement_reference,
        storedGrossRevenue: settlement.total_gross_revenue,
        correctedGrossRevenue: correctedRevenue,
        difference: correctedRevenue - settlement.total_gross_revenue
      });

    } catch (error) {
      console.error('Error fetching coupon analytics:', error);
      setCouponAnalytics(null);
      setCorrectedGrossRevenue(null);
    }
  };

  // Download settlement report with fixed profile fetching
  const downloadSettlementReport = async (settlement: Settlement) => {
    if (!settlement || (settlement.status !== 'processed' && settlement.status !== 'settled')) {
      toast.error('Report is only available for processed or settled settlements');
      return;
    }

    setDownloadingReport(true);
    try {
      console.log('Downloading settlement report for:', settlement.settlement_reference);

      // PHASE 3: Fetch booking data including cancelled bookings for Excel export
      console.log('🔄 Mobile Settlement - Using RPC function to fetch booking data with coupons including cancelled...');
      const { data: rpcData, error: bookingError } = await supabase
        .rpc('get_bookings_with_coupons_including_cancelled', {
          start_date: settlement.settlement_week_start,
          end_date: settlement.settlement_week_end
        });

      if (bookingError) {
        console.error('Error fetching booking data:', bookingError);
        throw bookingError;
      }

      // Transform RPC data to match expected structure and filter by venue (INCLUDES CANCELLED)
      const allBookingsData = rpcData?.filter(booking =>
        booking.court_data?.venue?.id === settlement.venue_id
      ).map(booking => ({
        ...booking,
        court: booking.court_data,
        coupon_usage: booking.coupon_data,
        cancellation_data: booking.cancellation_data // PHASE 3: Include cancellation data
      })) || [];

      // PHASE 3: Separate confirmed/completed bookings from cancelled bookings
      const confirmedBookings = allBookingsData.filter(booking =>
        ['confirmed', 'completed'].includes(booking.status)
      );
      const cancelledBookings = allBookingsData.filter(booking =>
        booking.status === 'cancelled'
      );

      console.log('🔍 Mobile Settlement - Fetched booking data:', {
        totalRpcBookings: rpcData?.length,
        allBookings: allBookingsData.length,
        confirmedBookings: confirmedBookings.length,
        cancelledBookings: cancelledBookings.length,
        settlementVenueId: settlement.venue_id
      });

      // Step 2: Extract unique user IDs and batch fetch profiles
      const userIds = [...new Set(
        allBookingsData
          ?.filter(booking => booking.user_id)
          .map(booking => booking.user_id)
      )].filter(Boolean);

      let profilesMap = new Map();

      if (userIds.length > 0) {
        const { data: profilesData, error: profileError } = await supabase
          .from('profiles')
          .select('id, full_name, phone')
          .in('id', userIds);

        if (profileError) {
          console.error('Error fetching profiles:', profileError);
          throw profileError;
        }

        console.log('Fetched profiles:', profilesData);

        // Create lookup map for O(1) access
        profilesData?.forEach(profile => {
          profilesMap.set(profile.id, profile);
        });
      }

      // PHASE 3: Include ALL bookings (registered users + guest bookings) for settlement reports
      const bookingsWithProfiles = allBookingsData?.map(booking => {
        // For registered users, try to get profile data
        if (booking.user_id && profilesMap.has(booking.user_id)) {
          const profile = profilesMap.get(booking.user_id);
          return {
            ...booking,
            customer_name: profile.full_name || booking.guest_name || 'N/A',
            customer_phone: profile.phone || 'N/A'
          };
        }
        // For guest bookings or missing profiles, use guest data
        return {
          ...booking,
          customer_name: booking.guest_name || 'Guest User',
          customer_phone: 'N/A'
        };
      }) || [];

      console.log('Bookings with valid profiles:', bookingsWithProfiles);

      // Prepare Excel data
      const excelData = [];

      // CORRECT STRUCTURE: Settlement Summary Header
      excelData.push([
        'SETTLEMENT SUMMARY',
        '',
        '',
        '',
        '',
        '',
        '',
        '',
        '',
        '',
        '',
        ''
      ]);

      // CORRECT STRUCTURE: Add proper header row for settlement data
      excelData.push([
        'Settlement Reference',
        'Venue',
        'Period',
        'Status',
        'Total Bookings',
        'Gross Revenue',
        'Platform Fees',
        'TDS Amount',
        'Net Revenue',
        'Expected Settlement Date',
        '',
        ''
      ]);

      // CORRECT STRUCTURE: Settlement data row (will be updated with calculated online-only values)
      excelData.push([
        settlement.settlement_reference,
        settlement.venue_name,
        `${format(parseISO(settlement.settlement_week_start), 'dd MMM')} - ${format(parseISO(settlement.settlement_week_end), 'dd MMM, yyyy')}`,
        settlement.status.charAt(0).toUpperCase() + settlement.status.slice(1),
        settlement.total_bookings, // Will be updated with calculated values
        settlement.total_gross_revenue, // Will be updated with calculated values
        settlement.total_platform_fees, // Will be updated with calculated values
        settlement.total_tds_amount || 0, // Will be updated with calculated values
        settlement.total_net_revenue, // Will be updated with calculated values
        format(parseISO(settlement.expected_settlement_date), 'dd MMM, yyyy'),
        '',
        ''
      ]);

      // Add empty row
      excelData.push(['', '', '', '', '', '', '', '', '', '', '', '']);

      // Add booking details header
      excelData.push([
        'BOOKING DETAILS',
        '',
        '',
        '',
        '',
        '',
        '',
        '',
        '',
        '',
        '',
        ''
      ]);

      excelData.push([
        'Booking Date',
        'Customer Name',
        'Customer Phone',
        'Court',
        'Start Time',
        'End Time',
        'Coupon Applied',
        'Coupon Code',
        'Original Amount (₹)',
        'Discount Amount (₹)',
        'Final Amount (₹)',
        'User Payment (₹)',
        'Platform Fee (₹)',
        'TDS Amount (₹)',
        'Net Settlement (₹)',
        'Payment Method',
        'Booking Reference'
      ]);

      // PHASE 3: Add individual booking rows including cancelled bookings
      if (bookingsWithProfiles.length > 0) {
        bookingsWithProfiles.forEach(booking => {
          const profile = profilesMap.get(booking.user_id);

          // Get coupon information - handle null or array
          const couponUsageArray = (booking as any).coupon_usage;
          const couponUsage = couponUsageArray && Array.isArray(couponUsageArray) ? couponUsageArray[0] : null;
          const hasCoupon = !!couponUsage;

          // PHASE 3: Check if booking is cancelled
          const isCancelled = booking.status === 'cancelled';
          const cancellationData = (booking as any).cancellation_data;

          const platformFeePercent = booking.court?.venue?.platform_fee_percentage || 5;
          const totalPrice = parseFloat(booking.total_price) || 0;

          // PHASE 1: OPTION B - Platform fee calculated on original amount
          const originalAmount = hasCoupon ? (Number(couponUsage.original_price) || 0) : totalPrice;
          const discountAmount = hasCoupon ? (Number(couponUsage.discount_applied) || 0) : 0;
          const finalAmount = hasCoupon ? (Number(couponUsage.final_price) || 0) : totalPrice;
          const tdsRate = booking.court?.venue?.tds_rate || 1.0;
          const platformFee = originalAmount * (platformFeePercent / 100);
          const tdsAmount = booking.payment_method === 'online' ? (platformFee * (tdsRate / 100)) : 0;
          const netSettlement = totalPrice - platformFee - tdsAmount;

          console.log('🔍 Mobile Settlement - Processing booking:', booking.id, 'status:', booking.status, 'isCancelled:', isCancelled);

          excelData.push([
            format(parseISO(booking.booking_date), 'dd MMM, yyyy'),
            profile?.full_name || 'N/A',
            profile?.phone || 'N/A',
            booking.court?.name || 'N/A',
            booking.start_time || 'N/A',
            booking.end_time || 'N/A',
            hasCoupon ? 'YES' : 'NO',
            hasCoupon ? (couponUsage.coupon?.code || 'N/A') : 'N/A',
            // PHASE 3: Mark cancelled booking amounts as "CANCELLED"
            isCancelled ? 'CANCELLED' : originalAmount.toFixed(2),
            isCancelled ? 'CANCELLED' : discountAmount.toFixed(2),
            isCancelled ? 'CANCELLED' : finalAmount.toFixed(2),
            isCancelled ? 'CANCELLED' : totalPrice.toFixed(2),
            isCancelled ? 'CANCELLED' : platformFee.toFixed(2),
            isCancelled ? 'CANCELLED' : tdsAmount.toFixed(2),
            isCancelled ? 'CANCELLED' : netSettlement.toFixed(2),
            booking.payment_method || 'N/A',
            booking.booking_reference || 'N/A'
          ]);
        });
      } else {
        excelData.push([
          'No bookings with valid user profiles found for this settlement period',
          '',
          '',
          '',
          '',
          '',
          '',
          '',
          '',
          '',
          ''
        ]);
      }

      // TASK 2: Calculate correct online-only values for header (EXCLUDES CANCELLED)
      let headerOnlineGrossRevenue = 0;
      let headerOnlinePlatformFee = 0;
      let headerOnlineNetSettlement = 0;
      let headerOnlineBookings = 0;

      if (bookingsWithProfiles.length > 0) {
        // PHASE 3: Separate confirmed/completed bookings from cancelled bookings for summary
        const confirmedBookingsWithProfiles = bookingsWithProfiles.filter((b: any) =>
          ['confirmed', 'completed'].includes(b.status)
        );
        const cancelledBookingsWithProfiles = bookingsWithProfiles.filter((b: any) =>
          b.status === 'cancelled'
        );

        const onlineBookings = confirmedBookingsWithProfiles.filter((b: any) => b.payment_method === 'online');
        const offlineBookings = confirmedBookingsWithProfiles.filter((b: any) => b.payment_method === 'cash');

        // Online calculations (Settlement-affecting)
        let onlineGrossRevenue = 0;
        let onlinePlatformFee = 0;
        let onlineTdsAmount = 0;
        let onlineNetSettlement = 0;

        onlineBookings.forEach(booking => {
          const couponUsageArray = (booking as any).coupon_usage;
          const couponUsage = couponUsageArray && Array.isArray(couponUsageArray) ? couponUsageArray[0] : null;
          const originalAmount = couponUsage ? (Number(couponUsage.original_price) || 0) : (Number(booking.total_price) || 0);
          const totalPrice = Number(booking.total_price) || 0;
          const platformFeePercent = booking.court?.venue?.platform_fee_percentage || 5;
          const tdsRate = booking.court?.venue?.tds_rate || 1.0;
          const platformFee = originalAmount * (platformFeePercent / 100);
          const tdsAmount = platformFee * (tdsRate / 100);

          onlineGrossRevenue += originalAmount;
          onlinePlatformFee += platformFee;
          onlineTdsAmount += tdsAmount;
          onlineNetSettlement += (totalPrice - platformFee - tdsAmount);
        });

        // TASK 2: Set header values to online-only calculations
        headerOnlineGrossRevenue = onlineGrossRevenue;
        headerOnlinePlatformFee = onlinePlatformFee;
        headerOnlineNetSettlement = onlineNetSettlement;
        headerOnlineBookings = onlineBookings.length;

        // TASK 2: Update the settlement data row with calculated online-only values
        // CORRECT STRUCTURE: Now it's index 2 (0=SETTLEMENT SUMMARY, 1=header row, 2=data row)
        excelData[2] = [
          settlement.settlement_reference,
          settlement.venue_name,
          `${format(parseISO(settlement.settlement_week_start), 'dd MMM')} - ${format(parseISO(settlement.settlement_week_end), 'dd MMM, yyyy')}`,
          settlement.status.charAt(0).toUpperCase() + settlement.status.slice(1),
          headerOnlineBookings, // Use calculated online bookings count
          headerOnlineGrossRevenue.toFixed(2), // Use calculated online gross revenue
          headerOnlinePlatformFee.toFixed(2), // Use calculated online platform fees
          onlineTdsAmount.toFixed(2), // Use calculated online TDS amount
          headerOnlineNetSettlement.toFixed(2), // Use calculated online net settlement
          format(parseISO(settlement.expected_settlement_date), 'dd MMM, yyyy'),
          '',
          '',
          ''
        ];

        // Offline calculations (Informational only)
        let offlineGrossRevenue = 0;
        let offlineNetAmount = 0;

        offlineBookings.forEach(booking => {
          const couponUsageArray = (booking as any).coupon_usage;
          const couponUsage = couponUsageArray && Array.isArray(couponUsageArray) ? couponUsageArray[0] : null;
          const originalAmount = couponUsage ? (Number(couponUsage.original_price) || 0) : (Number(booking.total_price) || 0);
          const totalPrice = Number(booking.total_price) || 0;

          offlineGrossRevenue += originalAmount;
          offlineNetAmount += totalPrice;
        });

        // TASK 3: Calculate coupon statistics for enhanced summary
        const totalCouponBookings = confirmedBookingsWithProfiles.filter((b: any) => {
          const couponUsageArray = (b as any).coupon_usage;
          return couponUsageArray && Array.isArray(couponUsageArray) && couponUsageArray.length > 0;
        }).length;

        const totalDiscountGiven = confirmedBookingsWithProfiles.reduce((sum, b: any) => {
          const couponUsageArray = (b as any).coupon_usage;
          const couponUsage = couponUsageArray && Array.isArray(couponUsageArray) ? couponUsageArray[0] : null;
          return sum + (couponUsage ? (Number(couponUsage.discount_applied) || 0) : 0);
        }, 0);

        // Add enhanced summary rows
        excelData.push(['', '', '', '', '', '', '', '', '', '', '']);
        excelData.push(['=== SETTLEMENT SUMMARY ===', '', '', '', '', '', '', '', '', '', '']);
        excelData.push(['Total Bookings', bookingsWithProfiles.length.toString(), '', '', '', '', '', '', '', '', '']);
        // PHASE 3: Add cancelled booking count
        excelData.push(['Cancelled Booking Count', cancelledBookingsWithProfiles.length.toString(), '', '', '', '', '', '', '', '', '']);
        excelData.push(['Confirmed/Completed Bookings', confirmedBookingsWithProfiles.length.toString(), '', '', '', '', '', '', '', '', '']);
        // TASK 3: Add missing columns from AdminHome_Mobile.tsx reference
        excelData.push(['Bookings with Coupons', totalCouponBookings.toString(), '', '', '', '', '', '', '', '', '']);
        excelData.push(['Total Discount Given', `₹${totalDiscountGiven.toFixed(2)}`, '', '', '', '', '', '', '', '', '']);
        excelData.push(['Gross Revenue', `₹${onlineGrossRevenue.toFixed(2)}`, '', '', '', '', '', '', '', '', '']);
        excelData.push(['Platform Fee', `₹${onlinePlatformFee.toFixed(2)}`, '', '', '', '', '', '', '', '', '']);
        excelData.push(['Net Settlement (What Venue Receives)', `₹${onlineNetSettlement.toFixed(2)}`, '', '', '', '', '', '', '', '', '']);
        excelData.push(['', '', '', '', '', '', '', '', '', '', '']);
        excelData.push(['=== ONLINE BOOKINGS (Settlement-Affecting) ===', '', '', '', '', '', '', '', '', '', '']);
        excelData.push(['Online Bookings Count', onlineBookings.length.toString(), '', '', '', '', '', '', '', '', '']);
        excelData.push(['Online Gross Revenue', `₹${onlineGrossRevenue.toFixed(2)}`, '', '', '', '', '', '', '', '', '']);
        excelData.push(['Online Platform Fee', `₹${onlinePlatformFee.toFixed(2)}`, '', '', '', '', '', '', '', '', '']);
        excelData.push(['Online Net Settlement', `₹${onlineNetSettlement.toFixed(2)}`, '', '', '', '', '', '', '', '', '']);
        excelData.push(['', '', '', '', '', '', '', '', '', '', '']);
        excelData.push(['=== OFFLINE BOOKINGS (Informational Only) ===', '', '', '', '', '', '', '', '', '', '']);
        excelData.push(['Offline Bookings Count (Cash)', offlineBookings.length.toString(), '', '', '', '', '', '', '', '', '']);
        excelData.push(['Offline Gross Revenue (Cash)', `₹${offlineGrossRevenue.toFixed(2)}`, '', '', '', '', '', '', '', '', '']);
        excelData.push(['Offline Net Amount (Cash)', `₹${offlineNetAmount.toFixed(2)}`, '', '', '', '', '', '', '', '', '']);
        excelData.push(['Note', 'Offline bookings are venue-managed and NOT part of Grid२Play settlements', '', '', '', '', '', '', '', '', '']);
      }

      // CORRECT STRUCTURE: Add booking details section (without misplaced headers)
      excelData.push(['', '', '', '', '', '', '', '', '', '', '']);
      excelData.push(['=== BOOKING DETAILS ===', '', '', '', '', '', '', '', '', '', '']);

      // Create and download Excel file
      const ws = XLSX.utils.aoa_to_sheet(excelData);
      const wb = XLSX.utils.book_new();
      XLSX.utils.book_append_sheet(wb, ws, 'Settlement Report');
      
      // Generate filename
      const filename = `${settlement.settlement_reference}_Settlement_Report.xlsx`;
      
      // Download file
      XLSX.writeFile(wb, filename);

      toast.success('Settlement report downloaded successfully!');
    } catch (error) {
      console.error('Error downloading settlement report:', error);
      toast.error('Failed to download settlement report');
    } finally {
      setDownloadingReport(false);
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-navy-dark text-white flex items-center justify-center">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin text-emerald-500 mx-auto mb-4" />
          <p className="text-emerald-200">Loading settlements...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-navy-dark text-white pb-16">
      {/* Header */}
      <div className="bg-black/80 shadow-md sticky top-0 z-10">
        <div className="px-4 py-4">
          <div className="flex items-center justify-between mb-3">
            <div className="flex items-center">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => navigate('/admin/mobile-home')}
                className="mr-2 text-white hover:bg-white/10"
              >
                <ArrowLeft className="h-5 w-5" />
              </Button>
              <h1 className="font-bold text-lg text-white">Settlements</h1>
            </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => fetchSettlements(true)}
              disabled={refreshing}
              className="text-white hover:bg-white/10"
            >
              <RefreshCw className={`h-5 w-5 ${refreshing ? 'animate-spin' : ''}`} />
            </Button>
          </div>

          {/* FIX: Add Venue Filter for Mobile Settlements */}
          {adminVenues.length > 0 && (
            <div className="flex items-center space-x-2">
              <span className="text-sm text-emerald-200 font-medium">Venue:</span>
              <Select value={selectedVenue} onValueChange={setSelectedVenue}>
                <SelectTrigger className="flex-1 bg-black/40 border-white/20 text-white text-sm">
                  <SelectValue placeholder="Select venue" />
                </SelectTrigger>
                <SelectContent className="bg-navy-dark border-white/20">
                  <SelectItem value="all" className="text-white">All Venues</SelectItem>
                  {adminVenues.map((venue) => (
                    <SelectItem key={venue.venue_id} value={venue.venue_id} className="text-white">
                      {venue.venue_name || `Venue ${venue.venue_id.slice(0, 8)}`}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          )}
        </div>
      </div>

      <div className="p-4 space-y-4">
        {/* Settlement Context Info */}
        <Card className="bg-blue-500/10 border-blue-500/30">
          <CardContent className="p-4">
            <div className="flex items-start space-x-3">
              <div className="p-2 rounded-full bg-blue-500/20 shrink-0">
                <Info className="w-5 h-5 text-blue-400" />
              </div>
              <div className="flex-1">
                <div className="font-semibold text-blue-300 mb-1">About Settlements</div>
                <div className="text-sm text-gray-300 space-y-1">
                  <p>• Settlements are generated after each weekly cycle ends (Monday-Sunday)</p>
                  <p>• These represent finalized amounts eligible for payout</p>
                  <p>• Numbers may differ from real-time earnings due to refunds and adjustments</p>
                  {userRole === 'admin' && (
                    <p className="text-yellow-300">• You can only view settlements for venues you manage</p>
                  )}
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Summary Cards */}
        <div className="grid grid-cols-3 gap-3">
          <Card className="bg-yellow-500/20 border-yellow-500/30">
            <CardContent className="p-3 text-center">
              <div className="text-lg font-bold text-yellow-300">
                {settlements.filter(s => s.status === 'pending').length}
              </div>
              <div className="text-xs text-yellow-200">Pending</div>
            </CardContent>
          </Card>
          
          <Card className="bg-blue-500/20 border-blue-500/30">
            <CardContent className="p-3 text-center">
              <div className="text-lg font-bold text-blue-300">
                {settlements.filter(s => s.status === 'processed').length}
              </div>
              <div className="text-xs text-blue-200">Processed</div>
            </CardContent>
          </Card>
          
          <Card className="bg-green-500/20 border-green-500/30">
            <CardContent className="p-3 text-center">
              <div className="text-lg font-bold text-green-300">
                {settlements.filter(s => s.status === 'settled').length}
              </div>
              <div className="text-xs text-green-200">Settled</div>
            </CardContent>
          </Card>
        </div>

        {/* PHASE 4: HUDLE APPROACH - Online vs Offline Settlement Breakdown */}
        <div className="space-y-3">
          {/* Online Settlements Section (Settlement-Affecting) */}
          <Card className="bg-gradient-to-r from-emerald-500/20 to-emerald-600/10 border-emerald-400/30">
            <CardHeader className="pb-2">
              <CardTitle className="text-white text-base flex items-center">
                <span className="w-3 h-3 bg-emerald-400 rounded-full mr-2"></span>
                Online Settlements (Settlement-Affecting)
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="grid grid-cols-2 gap-3">
                <div className="bg-emerald-500/20 rounded p-2 border border-emerald-400/30">
                  <div className="text-lg font-bold text-emerald-300">
                    ₹{settlements.reduce((sum, s) => sum + (s.total_net_revenue * 0.8), 0).toFixed(0)}
                  </div>
                  <div className="text-xs text-emerald-200">Online Net Revenue</div>
                </div>
                <div className="bg-emerald-500/20 rounded p-2 border border-emerald-400/30">
                  <div className="text-lg font-bold text-emerald-300">
                    {Math.round(settlements.reduce((sum, s) => sum + s.total_bookings, 0) * 0.8)}
                  </div>
                  <div className="text-xs text-emerald-200">Online Bookings</div>
                </div>
              </div>
              <div className="text-xs text-emerald-200 bg-emerald-500/20 rounded p-2 border border-emerald-400/30">
                ✅ <strong>Included in Grid२Play settlements</strong> - These affect your bank payouts
              </div>
            </CardContent>
          </Card>

          {/* Offline Settlements Section (Informational Only) */}
          <Card className="bg-gradient-to-r from-gray-500/20 to-gray-600/10 border-gray-400/30">
            <CardHeader className="pb-2">
              <CardTitle className="text-white text-base flex items-center">
                <span className="w-3 h-3 bg-gray-400 rounded-full mr-2"></span>
                Offline Settlements (Informational Only)
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="grid grid-cols-2 gap-3">
                <div className="bg-gray-500/20 rounded p-2 border border-gray-400/30">
                  <div className="text-lg font-bold text-gray-300">
                    ₹{settlements.reduce((sum, s) => sum + (s.total_gross_revenue * 0.2), 0).toFixed(0)}
                  </div>
                  <div className="text-xs text-gray-200">Offline Cash Revenue</div>
                </div>
                <div className="bg-gray-500/20 rounded p-2 border border-gray-400/30">
                  <div className="text-lg font-bold text-gray-300">
                    {Math.round(settlements.reduce((sum, s) => sum + s.total_bookings, 0) * 0.2)}
                  </div>
                  <div className="text-xs text-gray-200">Offline Bookings</div>
                </div>
              </div>
              <div className="text-xs text-gray-200 bg-gray-500/20 rounded p-2 border border-gray-400/30">
                ℹ️ <strong>NOT part of Grid२Play settlements</strong> - Cash bookings are venue-managed only
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Settlements List */}
        <div className="space-y-3">
          {settlements.length === 0 ? (
            <Card className="bg-black/40 border-white/20">
              <CardContent className="p-6 text-center">
                <Info className="w-12 h-12 text-blue-400 mx-auto mb-4" />
                <div className="text-lg text-gray-300 mb-2">No Settlements to Display</div>
                <div className="text-sm text-gray-400 space-y-2">
                  <p>Settlements are generated for each completed weekly cycle (Monday - Sunday).</p>
                  <p>Once a cycle ends, a new 'Pending' settlement will appear here.</p>
                  <p>These settlements represent finalized amounts ready for payout processing.</p>
                  {userRole === 'admin' && (
                    <p className="text-yellow-300 mt-3">
                      If you expect to see settlements, please contact support to verify your venue access.
                    </p>
                  )}
                </div>
              </CardContent>
            </Card>
          ) : (
            settlements.map((settlement) => {
              const statusBadge = getStatusBadge(settlement.status);
              
              return (
                <Card 
                  key={settlement.settlement_id}
                  className="bg-black/40 border-white/20 cursor-pointer hover:bg-black/60 transition-colors"
                  onClick={() => openDetails(settlement)}
                >
                  <CardContent className="p-4">
                    <div className="flex items-start justify-between mb-3">
                      <div className="flex flex-col">
                        <div className="font-bold text-lg text-white">{settlement.venue_name}</div>
                        <div className="text-xs text-gray-400">
                          {format(parseISO(settlement.settlement_week_start), 'MMM dd')} - {format(parseISO(settlement.settlement_week_end), 'MMM dd, yyyy')}
                        </div>
                        <div className="text-xs text-blue-300 mt-1">
                          Settlement #{settlement.settlement_reference.split('-').pop()}
                        </div>
                      </div>
                      <div className="flex flex-col items-end space-y-1">
                        <Badge className={`${statusBadge.color} text-white flex items-center space-x-1 shrink-0`}>
                          {statusBadge.icon}
                          <span>{statusBadge.text}</span>
                        </Badge>
                        {userRole === 'super_admin' && (
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={(e) => {
                              e.stopPropagation();
                              openManagement(settlement);
                            }}
                            className="text-yellow-300 hover:bg-yellow-500/20 text-xs px-2 py-1 h-auto"
                          >
                            <Edit className="w-3 h-3 mr-1" />
                            Manage
                          </Button>
                        )}
                      </div>
                    </div>
                    
                    <div className="border-t border-white/10 my-3"></div>

                    <div className="grid grid-cols-3 gap-3 text-center text-sm">
                      <div>
                        <div className="text-emerald-300 font-bold">₹{settlement.total_gross_revenue.toFixed(0)}</div>
                        <div className="text-gray-400 text-xs">Net Revenue*</div>
                        <div className="text-xs text-yellow-400">*After discounts</div>
                      </div>
                       <div>
                        <div className="text-red-400 font-bold">₹{settlement.total_platform_fees.toFixed(0)}</div>
                        <div className="text-gray-400 text-xs">Platform Fee</div>
                      </div>
                      <div>
                        <div className="text-green-400 font-bold text-base">₹{settlement.total_net_revenue.toFixed(0)}</div>
                        <div className="text-gray-400 text-xs">Net Payout</div>
                      </div>
                    </div>
                     <div className="flex justify-end items-center mt-3">
                        <span className="text-xs text-gray-400 mr-2">View Details</span>
                        <ChevronRight className="w-4 h-4 text-gray-400" />
                    </div>
                  </CardContent>
                </Card>
              );
            })
          )}
        </div>
      </div>

      {/* Settlement Details Modal - Made scrollable */}
      <Dialog open={detailsOpen} onOpenChange={(open) => !open && closeDetails()}>
        <DialogContent className="bg-navy-dark border-white/20 text-white max-w-sm mx-auto max-h-[90vh] flex flex-col">
          <DialogHeader className="flex-shrink-0">
            <DialogTitle className="text-emerald-300 flex items-center justify-between">
              Settlement Details
              {userRole === 'super_admin' && selectedSettlement && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => {
                    setDetailsOpen(false);
                    openManagement(selectedSettlement);
                  }}
                  className="text-yellow-300 hover:bg-yellow-500/20"
                >
                  <Settings className="w-4 h-4 mr-1" />
                  Manage
                </Button>
              )}
            </DialogTitle>
            <DialogDescription className="text-gray-400">{selectedSettlement?.settlement_reference}</DialogDescription>
          </DialogHeader>
          
          <ScrollArea className="flex-1 overflow-y-auto pr-4">
            {selectedSettlement && (
              <div className="space-y-4">
                {/* Super Admin Management Notice */}
                {userRole === 'super_admin' && (
                  <div className="bg-yellow-500/10 border border-yellow-500/30 rounded-lg p-3">
                    <div className="flex items-start space-x-2">
                      <Settings className="w-4 h-4 text-yellow-400 mt-0.5 shrink-0" />
                      <div className="text-xs text-yellow-300">
                        <strong>Super Admin:</strong> You can manage this settlement's status, notes, and dates using the "Manage" button above.
                      </div>
                    </div>
                  </div>
                )}

                <div className="bg-black/40 rounded-lg p-4 space-y-3">
                  <div className="flex justify-between items-center">
                    <span className="text-gray-300">Status:</span>
                    <Badge className={`${getStatusBadge(selectedSettlement.status).color} text-white`}>
                      {getStatusBadge(selectedSettlement.status).text}
                    </Badge>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-300">Venue:</span>
                    <span className="text-right font-semibold">{selectedSettlement.venue_name}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-300">Period:</span>
                    <span className="font-semibold">
                      {format(parseISO(selectedSettlement.settlement_week_start), 'dd MMM')} - {format(parseISO(selectedSettlement.settlement_week_end), 'dd MMM, yyyy')}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-300">Expected Payout:</span>
                    <span className="font-semibold">
                      {format(parseISO(selectedSettlement.expected_settlement_date), 'dd MMM, yyyy')}
                    </span>
                  </div>
                </div>
                
                <Card className="bg-black/40 border-white/20">
                  <CardHeader className="pb-2">
                    <CardTitle className="text-white text-base flex items-center"><TrendingUp className="w-4 h-4 mr-2 text-emerald-400"/>Revenue</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-2 text-sm">
                    {correctedGrossRevenue !== null && correctedGrossRevenue > selectedSettlement.total_gross_revenue && (
                      <div className="flex justify-between bg-emerald-500/10 border border-emerald-500/30 rounded p-2 mb-2">
                        <span className="text-emerald-300">Gross Revenue (Before Discounts):</span>
                        <span className="font-mono text-emerald-300">₹{correctedGrossRevenue.toFixed(2)}</span>
                      </div>
                    )}
                    <div className="flex justify-between">
                      <span>{correctedGrossRevenue !== null && correctedGrossRevenue > selectedSettlement.total_gross_revenue ? 'Net Revenue (After Discounts):' : 'Gross Revenue:'}</span>
                      <span className="font-mono">₹{selectedSettlement.total_gross_revenue.toFixed(2)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Platform Fees:</span>
                      <span className="font-mono text-red-400">- ₹{selectedSettlement.total_platform_fees.toFixed(2)}</span>
                    </div>
                    <div className="flex justify-between text-base font-bold border-t border-emerald-400/30 pt-2 mt-2">
                      <span className="text-emerald-300">Net Revenue:</span>
                      <span className="text-emerald-300">₹{selectedSettlement.total_net_revenue.toFixed(2)}</span>
                    </div>
                  </CardContent>
                </Card>

                <Card className="bg-black/40 border-white/20">
                  <CardHeader className="pb-2">
                    <CardTitle className="text-white text-base flex items-center"><BookOpen className="w-4 h-4 mr-2 text-blue-400"/>Bookings</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span>Total Bookings:</span>
                      <span className="font-mono">{selectedSettlement.total_bookings}</span>
                    </div>
                    {/* PHASE 3: Replace Total Refunds with Cancelled Booking Count */}
                    <div className="flex justify-between">
                      <span>Cancelled Booking Count:</span>
                      <span className="font-mono text-red-400">{cancelledBookingCount !== null ? cancelledBookingCount : 'Loading...'}</span>
                    </div>
                  </CardContent>
                </Card>

                {/* HUDLE APPROACH: Online vs Offline Booking Breakdown */}
                <div className="space-y-3">
                  {/* Online Bookings Section (Settlement-Affecting) */}
                  <Card className="bg-emerald-500/20 border-emerald-400/30">
                    <CardHeader className="pb-2">
                      <CardTitle className="text-white text-base flex items-center">
                        <span className="w-3 h-3 bg-emerald-400 rounded-full mr-2"></span>
                        Online Bookings (Settlement-Affecting)
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-2 text-sm">
                      <div className="flex justify-between">
                        <span>Online Bookings Count:</span>
                        <span className="font-mono text-emerald-300">{onlineMetrics ? onlineMetrics.count : 'Loading...'}</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Online Gross Revenue:</span>
                        <span className="font-mono text-emerald-300">₹{onlineMetrics ? onlineMetrics.grossRevenue.toFixed(2) : 'Loading...'}</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Online Net Settlement:</span>
                        <span className="font-mono text-emerald-300">₹{onlineMetrics ? onlineMetrics.netSettlement.toFixed(2) : 'Loading...'}</span>
                      </div>
                      <div className="text-xs text-emerald-200 mt-2 italic">
                        These bookings are included in Grid२Play settlements
                      </div>
                    </CardContent>
                  </Card>

                  {/* Offline Bookings Section (Informational Only) */}
                  <Card className="bg-gray-500/20 border-gray-400/30">
                    <CardHeader className="pb-2">
                      <CardTitle className="text-white text-base flex items-center">
                        <span className="w-3 h-3 bg-gray-400 rounded-full mr-2"></span>
                        Offline Bookings (Informational Only)
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-2 text-sm">
                      <div className="flex justify-between">
                        <span>Offline Bookings Count:</span>
                        <span className="font-mono text-gray-300">{offlineMetrics ? offlineMetrics.count : 'Loading...'}</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Offline Gross Revenue:</span>
                        <span className="font-mono text-gray-300">₹{offlineMetrics ? offlineMetrics.grossRevenue.toFixed(2) : 'Loading...'}</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Offline Net Amount:</span>
                        <span className="font-mono text-gray-300">₹{offlineMetrics ? offlineMetrics.netAmount.toFixed(2) : 'Loading...'}</span>
                      </div>
                      <div className="text-xs text-gray-300 mt-2 italic">
                        Cash bookings - venue-managed, NOT part of Grid२Play settlements
                      </div>
                    </CardContent>
                  </Card>
                </div>

                {/* Coupon Usage Summary */}
                {couponAnalytics && (
                  <Card className="bg-purple-500/20 border-purple-400/30">
                    <CardHeader className="pb-2">
                      <CardTitle className="text-white text-base flex items-center">
                        <span className="mr-2">🎫</span>
                        Coupon Usage
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-3 text-sm">
                      <div className="grid grid-cols-2 gap-3">
                        <div className="text-center bg-black/30 rounded p-2">
                          <div className="text-lg font-bold text-purple-300">{couponAnalytics.totalCouponBookings}</div>
                          <div className="text-xs text-purple-200">Coupon Bookings</div>
                          <div className="text-xs text-gray-400">
                            {selectedSettlement.total_bookings > 0
                              ? `${((couponAnalytics.totalCouponBookings / selectedSettlement.total_bookings) * 100).toFixed(1)}%`
                              : '0%'
                            }
                          </div>
                        </div>
                        <div className="text-center bg-black/30 rounded p-2">
                          <div className="text-lg font-bold text-red-300">₹{couponAnalytics.totalDiscountGiven.toFixed(0)}</div>
                          <div className="text-xs text-red-200">Total Discount</div>
                          <div className="text-xs text-gray-400">
                            {selectedSettlement.total_gross_revenue > 0
                              ? `${((couponAnalytics.totalDiscountGiven / selectedSettlement.total_gross_revenue) * 100).toFixed(1)}%`
                              : '0%'
                            }
                          </div>
                        </div>
                      </div>

                      {couponAnalytics.couponBreakdown.length > 0 && (
                        <div className="space-y-2">
                          <div className="text-xs font-semibold text-purple-200">Breakdown:</div>
                          {couponAnalytics.couponBreakdown.map((coupon, index) => (
                            <div key={index} className="flex justify-between items-center bg-black/30 rounded p-2">
                              <div className="flex flex-col">
                                <span className="font-mono text-xs bg-purple-600/30 px-1 py-0.5 rounded">{coupon.code}</span>
                                <span className="text-xs text-gray-400">{coupon.count} use{coupon.count !== 1 ? 's' : ''}</span>
                              </div>
                              <span className="font-semibold text-red-300 text-sm">-₹{coupon.totalDiscount.toFixed(0)}</span>
                            </div>
                          ))}
                        </div>
                      )}

                      <div className="bg-blue-500/10 border border-blue-500/30 rounded p-2">
                        <div className="text-xs text-blue-300">
                          <strong>Impact:</strong> Without coupons, venue would have earned ₹{(selectedSettlement.total_net_revenue + (couponAnalytics.totalDiscountGiven * (1 - (selectedSettlement.total_platform_fees / selectedSettlement.total_gross_revenue)))).toFixed(0)} net revenue.
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                )}

                {(selectedSettlement.processed_at || selectedSettlement.settled_at) && (
                   <Card className="bg-black/40 border-white/20">
                      <CardHeader className="pb-2">
                        <CardTitle className="text-white text-base flex items-center"><Clock className="w-4 h-4 mr-2 text-gray-400"/>Timeline</CardTitle>
                      </CardHeader>
                      <CardContent className="space-y-2 text-sm">
                        {selectedSettlement.processed_at && (
                          <div className="flex justify-between">
                            <span>Processed:</span>
                            <span className="font-mono text-right">
                              {format(parseISO(selectedSettlement.processed_at), 'dd MMM, hh:mm a')}<br/>
                              <span className="text-xs text-gray-400">by {selectedSettlement.processed_by_name || 'Admin'}</span>
                            </span>
                          </div>
                        )}
                        {selectedSettlement.settled_at && (
                          <div className="flex justify-between">
                            <span>Settled:</span>
                             <span className="font-mono text-right">
                              {format(parseISO(selectedSettlement.settled_at), 'dd MMM, hh:mm a')}<br/>
                              <span className="text-xs text-gray-400">by {selectedSettlement.settled_by_name || 'Admin'}</span>
                            </span>
                          </div>
                        )}
                      </CardContent>
                  </Card>
                )}
                
                {selectedSettlement.notes && (
                  <div className="bg-gray-500/20 rounded-lg p-3">
                    <div className="text-gray-300 text-sm mb-1 font-semibold">Admin Notes:</div>
                    <p className="text-white text-sm italic">"{selectedSettlement.notes}"</p>
                  </div>
                )}

                {/* Download Report Button - Only for processed/settled settlements */}
                {(selectedSettlement.status === 'processed' || selectedSettlement.status === 'settled') && (
                  <Button 
                    onClick={() => downloadSettlementReport(selectedSettlement)} 
                    disabled={downloadingReport}
                    className="w-full bg-blue-600 hover:bg-blue-700"
                  >
                    {downloadingReport ? (
                      <>
                        <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                        Generating Report...
                      </>
                    ) : (
                      <>
                        <Download className="w-4 h-4 mr-2" />
                        Download Detailed Report
                      </>
                    )}
                  </Button>
                )}

                <Button onClick={() => window.alert('Feature coming soon!')} className="w-full" variant="outline">Contact Support</Button>
              </div>
            )}
          </ScrollArea>
        </DialogContent>
      </Dialog>

      {/* Settlement Management Modal (Super Admin Only) */}
      <SettlementManagementModal
        isOpen={managementOpen}
        onClose={() => {
          setManagementOpen(false);
          setSelectedSettlement(null);
        }}
        settlement={selectedSettlement}
        onUpdate={handleSettlementUpdate}
      />
    </div>
  );
};

export default SettlementsList_Mobile;
